#!/usr/bin/env python3
"""
Cloudflare IP 优化器
复制Cloudflare Worker脚本的IP优化功能。
从各种源获取IP，测试延迟，并按国家组织结果。
"""

import asyncio
import aiohttp
import ipaddress
import json
import random
import time
import logging
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path
import argparse

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 为了调试，我们也启用一些debug信息
logging.getLogger('aiohttp').setLevel(logging.WARNING)  # 减少aiohttp的日志噪音

@dataclass
class IPResult:
    """表示IP测试结果"""
    ip: str
    port: int
    latency: float
    colo: str
    country_code: str
    ip_type: str  # 'official' 或 'proxy'

    def __str__(self):
        return f"{self.ip}:{self.port}#{self.country_code} {self.ip_type} {self.latency:.0f}ms"

class CloudflareIPOptimizer:
    """Cloudflare IP优化的主类"""
    
    def __init__(self, max_ips: int = 512, timeout: int = 5, max_concurrent: int = 32):
        self.max_ips = max_ips
        self.timeout = timeout
        self.max_concurrent = max_concurrent
        self.cloudflare_locations = {}
        self.nip_domain = "nip.io"  # 默认备用域名

    async def load_cloudflare_locations(self) -> None:
        """加载Cloudflare位置数据用于国家映射"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get('https://speed.cloudflare.com/locations') as response:
                    if response.status == 200:
                        locations = await response.json()
                        self.cloudflare_locations = {loc['iata']: loc['cca2'] for loc in locations}
                        logger.info(f"已加载 {len(self.cloudflare_locations)} 个Cloudflare位置")
                    else:
                        logger.warning("加载Cloudflare位置失败，使用备用方案")
        except Exception as e:
            logger.error(f"加载Cloudflare位置时出错: {e}")

    async def get_nip_domain(self) -> str:
        """从DNS TXT记录获取当前nip域名"""
        try:
            # 这需要DNS解析，目前使用备用方案
            return "nip.io"
        except Exception as e:
            logger.error(f"获取nip域名时出错: {e}")
            return "nip.io"
    
    def generate_ips_from_cidr(self, cidr: str, count: int = 1) -> List[str]:
        """从CIDR块生成随机IP"""
        try:
            network = ipaddress.IPv4Network(cidr, strict=False)
            # 排除网络地址和广播地址
            hosts = list(network.hosts())
            if len(hosts) == 0:
                return []

            # 将数量限制为可用主机数
            actual_count = min(count, len(hosts))
            return [str(ip) for ip in random.sample(hosts, actual_count)]
        except Exception as e:
            logger.error(f"从CIDR {cidr} 生成IP时出错: {e}")
            return []

    async def fetch_ip_source(self, source: str, target_port: str = "443") -> List[str]:
        """从各种源获取IP"""
        urls = {
            'official': 'https://www.cloudflare.com/ips-v4/',
            'as13335': 'https://raw.githubusercontent.com/ipverse/asn-ip/master/as/13335/ipv4-aggregated.txt',
            'as209242': 'https://raw.githubusercontent.com/ipverse/asn-ip/master/as/209242/ipv4-aggregated.txt',
            'as24429': 'https://raw.githubusercontent.com/ipverse/asn-ip/master/as/24429/ipv4-aggregated.txt',
            'as35916': 'https://raw.githubusercontent.com/ipverse/asn-ip/master/as/35916/ipv4-aggregated.txt',
            'as199524': 'https://raw.githubusercontent.com/ipverse/asn-ip/master/as/199524/ipv4-aggregated.txt',
            'cm': 'https://raw.githubusercontent.com/cmliu/cmliu/main/CF-CIDR.txt',
            'proxyip': 'https://raw.githubusercontent.com/cmliu/ACL4SSR/main/baipiao.txt'
        }
        
        if source not in urls:
            logger.error(f"未知的IP源: {source}")
            return []

        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(urls[source]) as response:
                    if response.status == 200:
                        text = await response.text()
                        return await self._process_ip_source(text, source, target_port)
                    else:
                        logger.error(f"获取 {source} 失败: HTTP {response.status}")
                        return []
        except Exception as e:
            logger.error(f"获取 {source} 时出错: {e}")
            return []

    async def _process_ip_source(self, text: str, source: str, target_port: str) -> List[str]:
        """根据源类型处理IP源文本"""
        lines = [line.strip() for line in text.split('\n') if line.strip() and not line.startswith('#')]

        if source == 'proxyip':
            # 处理代理IP列表格式 (IP:PORT#COMMENT)
            valid_ips = []
            for line in lines:
                parsed_ip = self._parse_proxy_ip_line(line, target_port)
                if parsed_ip:
                    valid_ips.append(parsed_ip)

            # 限制到max_ips并在需要时打乱顺序
            if len(valid_ips) > self.max_ips:
                valid_ips = random.sample(valid_ips, self.max_ips)

            return valid_ips
        else:
            # 处理CIDR格式
            ips = set()
            round_num = 1

            while len(ips) < self.max_ips and round_num <= 100:
                for cidr in lines:
                    if len(ips) >= self.max_ips:
                        break

                    cidr_ips = self.generate_ips_from_cidr(cidr.strip(), round_num)
                    ips.update(cidr_ips)

                round_num += 1

            return list(ips)[:self.max_ips]

    def _parse_proxy_ip_line(self, line: str, target_port: str) -> Optional[str]:
        """解析代理IP行格式"""
        try:
            # 处理注释部分
            main_part = line
            comment = ""
            if '#' in line:
                main_part, comment = line.split('#', 1)
                main_part = main_part.strip()
                comment = comment.strip()

            # 处理端口部分
            if ':' in main_part:
                ip, port = main_part.split(':', 1)
                ip = ip.strip()
                port = port.strip()
            else:
                ip = main_part.strip()
                port = "443"

            # 验证IP格式
            try:
                ipaddress.IPv4Address(ip)
            except ipaddress.AddressValueError:
                return None

            # 检查端口匹配
            if port != target_port:
                return None

            # 返回格式化结果
            result = f"{ip}:{port}"
            if comment:
                result += f"#{comment}"

            return result
        except Exception:
            return None
    
    def _ip_to_nip_format(self, ip: str) -> str:
        """Convert IP to nip.io format (hex encoding)"""
        try:
            parts = ip.split('.')
            hex_parts = [f"{int(part):02x}" for part in parts]
            return ''.join(hex_parts)
        except Exception:
            return ip
    
    async def test_single_ip(self, ip: str, port: int) -> Optional[IPResult]:
        """Test a single IP for latency and get location info"""
        max_attempts = 3

        for attempt in range(max_attempts):
            try:
                start_time = time.time()

                # 方法1: 尝试使用TCP连接测试
                result = await self._test_tcp_connection(ip, port, start_time)
                if result:
                    return result

                # 方法2: 尝试使用HTTP请求测试
                result = await self._test_http_connection(ip, port, start_time)
                if result:
                    return result

            except Exception as e:
                if attempt < max_attempts - 1:
                    await asyncio.sleep(0.2)  # Brief delay before retry
                else:
                    # 改为info级别，这样可以看到错误信息
                    logger.info(f"IP {ip}:{port} failed after {max_attempts} attempts: {str(e)[:100]}")

        return None

    async def _test_tcp_connection(self, ip: str, port: int, start_time: float) -> Optional[IPResult]:
        """使用TCP连接测试IP"""
        try:
            # 首先测试基本的TCP连接
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.timeout)

            result = sock.connect_ex((ip, port))
            if result != 0:
                sock.close()
                logger.debug(f"TCP connection failed for {ip}:{port}: {result}")
                return None

            sock.close()
            tcp_latency = (time.time() - start_time) * 1000

            # TCP连接成功，现在尝试SSL连接和HTTP请求
            try:
                reader, writer = await asyncio.wait_for(
                    asyncio.open_connection(ip, port, ssl=True),
                    timeout=self.timeout
                )

                # 发送HTTP请求获取trace信息
                request = f"GET /cdn-cgi/trace HTTP/1.1\r\nHost: cloudflare.com\r\nConnection: close\r\n\r\n"
                writer.write(request.encode())
                await writer.drain()

                # 读取响应
                response_data = await asyncio.wait_for(reader.read(4096), timeout=3)
                writer.close()
                await writer.wait_closed()

                response_text = response_data.decode('utf-8', errors='ignore')

                # 查找trace数据
                if '\r\n\r\n' in response_text:
                    trace_text = response_text.split('\r\n\r\n', 1)[1]
                    trace_data = self._parse_trace_response(trace_text)

                    if trace_data and 'colo' in trace_data:
                        colo = trace_data['colo']
                        country_code = self.cloudflare_locations.get(colo, colo)

                        final_latency = (time.time() - start_time) * 1000

                        return IPResult(
                            ip=ip,
                            port=port,
                            latency=final_latency,
                            colo=colo,
                            country_code=country_code,
                            ip_type='official'
                        )

                # 如果没有trace数据，至少返回连接成功的结果
                logger.debug(f"No trace data found for {ip}:{port}, but connection successful")
                return IPResult(
                    ip=ip,
                    port=port,
                    latency=tcp_latency,
                    colo='UNKNOWN',
                    country_code='CN',  # 假设是中国，因为我们在中国测试
                    ip_type='official'
                )

            except Exception as ssl_e:
                logger.debug(f"SSL/HTTP test failed for {ip}:{port}: {ssl_e}")
                # SSL失败但TCP成功，仍然返回结果
                return IPResult(
                    ip=ip,
                    port=port,
                    latency=tcp_latency,
                    colo='TCP_ONLY',
                    country_code='CN',  # 假设是中国
                    ip_type='official'
                )

        except Exception as e:
            logger.debug(f"TCP test failed for {ip}:{port}: {e}")
            return None

    async def _test_http_connection(self, ip: str, port: int, start_time: float) -> Optional[IPResult]:
        """使用HTTP请求测试IP"""
        try:
            # 构造请求头
            headers = {
                'Host': 'cloudflare.com',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': 'text/plain',
                'Connection': 'close'
            }

            # 创建SSL上下文，跳过证书验证
            import ssl
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE

            connector = aiohttp.TCPConnector(
                ssl=ssl_context,
                limit=1,
                limit_per_host=1,
                enable_cleanup_closed=True
            )

            async with aiohttp.ClientSession(
                connector=connector,
                timeout=aiohttp.ClientTimeout(total=self.timeout, connect=self.timeout//2)
            ) as session:
                url = f"https://{ip}:{port}/cdn-cgi/trace"
                async with session.get(url, headers=headers) as response:
                    latency = (time.time() - start_time) * 1000

                    if response.status == 200:
                        trace_text = await response.text()

                        trace_data = self._parse_trace_response(trace_text)
                        if trace_data and 'colo' in trace_data:
                            colo = trace_data['colo']
                            country_code = self.cloudflare_locations.get(colo, colo)

                            logger.debug(f"HTTP test successful for {ip}:{port}: {latency:.0f}ms, colo={colo}")

                            return IPResult(
                                ip=ip,
                                port=port,
                                latency=latency,
                                colo=colo,
                                country_code=country_code,
                                ip_type='official'
                            )
                        else:
                            logger.debug(f"HTTP response missing colo data for {ip}:{port}")
                    else:
                        logger.debug(f"HTTP test failed for {ip}:{port}: HTTP {response.status}")

        except Exception as e:
            logger.debug(f"HTTP test failed for {ip}:{port}: {e}")
            return None
    
    def _parse_trace_response(self, trace_text: str) -> Dict[str, str]:
        """Parse Cloudflare trace response"""
        data = {}
        for line in trace_text.split('\n'):
            line = line.strip()
            if line and '=' in line:
                key, value = line.split('=', 1)
                data[key] = value
        return data

    async def test_ips_with_concurrency(self, ips: List[str], port: int) -> List[IPResult]:
        """Test multiple IPs with controlled concurrency"""
        semaphore = asyncio.Semaphore(self.max_concurrent)

        async def test_with_semaphore(ip: str) -> Optional[IPResult]:
            async with semaphore:
                return await self.test_single_ip(ip, port)

        logger.info(f"Testing {len(ips)} IPs with concurrency {self.max_concurrent}")

        # Create tasks for all IPs
        tasks = [test_with_semaphore(ip.split(':')[0] if ':' in ip else ip) for ip in ips]

        # Execute with progress tracking
        results = []
        completed = 0

        for coro in asyncio.as_completed(tasks):
            result = await coro
            completed += 1

            if result:
                results.append(result)

            # Log progress every 50 completed tests
            if completed % 50 == 0 or completed == len(tasks):
                logger.info(f"Progress: {completed}/{len(tasks)} tested, {len(results)} valid")

        # Sort by latency
        results.sort(key=lambda x: x.latency)
        logger.info(f"Testing completed: {len(results)} valid IPs found")

        return results

    def organize_results_by_country(self, results: List[IPResult]) -> Dict[str, List[IPResult]]:
        """Organize results by country code"""
        country_results = {}

        for result in results:
            country = result.country_code
            if country not in country_results:
                country_results[country] = []
            country_results[country].append(result)

        # Sort each country's results by latency
        for country in country_results:
            country_results[country].sort(key=lambda x: x.latency)

        return country_results

    def save_results(self, results: List[IPResult], output_dir: str = "results") -> None:
        """Save results to files organized by country"""
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)

        # Organize by country
        country_results = self.organize_results_by_country(results)

        # Save overall results
        with open(output_path / "all_results.txt", "w", encoding="utf-8") as f:
            f.write(f"# Cloudflare IP Optimization Results\n")
            f.write(f"# Generated at: {time.strftime('%Y-%m-%d %H:%M:%S UTC', time.gmtime())}\n")
            f.write(f"# Total IPs tested: {len(results)}\n")
            f.write(f"# Countries found: {len(country_results)}\n\n")

            for result in results:
                f.write(f"{result}\n")

        # Save by country
        for country, country_ips in country_results.items():
            filename = f"{country.lower()}_ips.txt"
            with open(output_path / filename, "w", encoding="utf-8") as f:
                f.write(f"# Cloudflare IPs for {country}\n")
                f.write(f"# Generated at: {time.strftime('%Y-%m-%d %H:%M:%S UTC', time.gmtime())}\n")
                f.write(f"# Total IPs: {len(country_ips)}\n\n")

                for result in country_ips:
                    f.write(f"{result}\n")

        # Save summary JSON
        summary = {
            "generated_at": time.strftime('%Y-%m-%d %H:%M:%S UTC', time.gmtime()),
            "total_ips": len(results),
            "countries": {
                country: {
                    "count": len(ips),
                    "best_latency": min(ip.latency for ip in ips),
                    "avg_latency": sum(ip.latency for ip in ips) / len(ips)
                }
                for country, ips in country_results.items()
            }
        }

        with open(output_path / "summary.json", "w", encoding="utf-8") as f:
            json.dump(summary, f, indent=2)

        logger.info(f"Results saved to {output_path}")
        logger.info(f"Countries found: {', '.join(sorted(country_results.keys()))}")

    async def optimize_ips(self, ip_sources: List[str], ports: List[int], output_dir: str = "results") -> None:
        """Main optimization function"""
        logger.info("Starting Cloudflare IP optimization")

        # Load Cloudflare locations
        await self.load_cloudflare_locations()

        # Get nip domain
        self.nip_domain = await self.get_nip_domain()

        all_results = []

        for port in ports:
            logger.info(f"Testing port {port}")

            for source in ip_sources:
                logger.info(f"Fetching IPs from source: {source}")
                ips = await self.fetch_ip_source(source, str(port))

                if not ips:
                    logger.warning(f"No IPs found for source {source}")
                    continue

                logger.info(f"Found {len(ips)} IPs from {source}")

                # Test IPs
                results = await self.test_ips_with_concurrency(ips, port)
                all_results.extend(results)

                logger.info(f"Source {source} completed: {len(results)} valid IPs")

        if all_results:
            # Sort all results by latency
            all_results.sort(key=lambda x: x.latency)

            # Save results
            self.save_results(all_results, output_dir)

            # Log summary
            country_count = len(set(result.country_code for result in all_results))
            best_latency = all_results[0].latency if all_results else 0

            logger.info(f"Optimization completed!")
            logger.info(f"Total valid IPs: {len(all_results)}")
            logger.info(f"Countries found: {country_count}")
            logger.info(f"Best latency: {best_latency:.0f}ms")
        else:
            logger.error("No valid IPs found!")

async def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="Cloudflare IP Optimizer")
    parser.add_argument("--sources", nargs="+",
                       default=["official"],  # 先只测试官方源
                       choices=["official", "as13335", "as209242", "as24429", "as35916", "as199524", "cm", "proxyip"],
                       help="IP sources to use")
    parser.add_argument("--ports", nargs="+", type=int,
                       default=[443],  # 先只测试443端口
                       help="Ports to test")
    parser.add_argument("--max-ips", type=int, default=50,  # 减少测试数量
                       help="Maximum IPs to test per source")
    parser.add_argument("--timeout", type=int, default=10,  # 增加超时时间
                       help="Timeout for each IP test in seconds")
    parser.add_argument("--concurrent", type=int, default=10,  # 减少并发数
                       help="Maximum concurrent tests")
    parser.add_argument("--output", default="results",
                       help="Output directory for results")
    parser.add_argument("--verbose", "-v", action="store_true",
                       help="Enable verbose debug output")

    args = parser.parse_args()

    # 如果启用了verbose模式，设置debug级别
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.info("Verbose mode enabled")

    optimizer = CloudflareIPOptimizer(
        max_ips=args.max_ips,
        timeout=args.timeout,
        max_concurrent=args.concurrent
    )

    await optimizer.optimize_ips(args.sources, args.ports, args.output)

if __name__ == "__main__":
    asyncio.run(main())
