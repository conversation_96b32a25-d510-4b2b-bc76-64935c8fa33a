#!/usr/bin/env python3
"""
全面的Cloudflare IP测试
使用多个IP源进行测试
"""

import asyncio
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ip_optimizer import CloudflareIPOptimizer

async def comprehensive_test():
    """全面测试多个IP源"""
    
    print("=== Comprehensive Cloudflare IP Test ===\n")
    
    optimizer = CloudflareIPOptimizer(
        max_ips=30,  # 每个源测试30个IP
        timeout=8,   # 8秒超时
        max_concurrent=8  # 8个并发
    )
    
    # 要测试的源
    sources_to_test = [
        "official",  # Cloudflare官方IP
        "cm",        # cmliu的CIDR列表
    ]
    
    # 要测试的端口
    ports_to_test = [443, 2053, 2083, 2087, 2096, 8443]
    
    print(f"Testing sources: {sources_to_test}")
    print(f"Testing ports: {ports_to_test}")
    print(f"Max IPs per source: {optimizer.max_ips}")
    print(f"Timeout: {optimizer.timeout}s")
    print(f"Concurrency: {optimizer.max_concurrent}")
    print()
    
    # 运行优化
    await optimizer.optimize_ips(sources_to_test, ports_to_test, "comprehensive_results")

if __name__ == "__main__":
    asyncio.run(comprehensive_test())
