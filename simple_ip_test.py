#!/usr/bin/env python3
"""
简单的Cloudflare IP测试工具
用于调试IP连接问题
"""

import asyncio
import aiohttp
import time
import ssl
import socket
from typing import Optional

async def test_ip_simple(ip: str, port: int = 443, timeout: int = 10) -> Optional[float]:
    """简单测试IP连接"""
    print(f"Testing {ip}:{port}...")
    
    # 方法1: TCP连接测试
    try:
        start_time = time.time()
        
        # 创建socket连接
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        
        result = sock.connect_ex((ip, port))
        if result == 0:
            latency = (time.time() - start_time) * 1000
            print(f"  ✓ TCP connection successful: {latency:.0f}ms")
            sock.close()
            
            # 尝试HTTPS请求
            return await test_https_request(ip, port, timeout)
        else:
            print(f"  ✗ TCP connection failed: {result}")
            sock.close()
            return None
            
    except Exception as e:
        print(f"  ✗ TCP test error: {e}")
        return None

async def test_https_request(ip: str, port: int, timeout: int) -> Optional[float]:
    """测试HTTPS请求"""
    try:
        start_time = time.time()
        
        # 创建SSL上下文
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE
        
        connector = aiohttp.TCPConnector(
            ssl=ssl_context,
            limit=1,
            limit_per_host=1
        )
        
        headers = {
            'Host': 'cloudflare.com',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        async with aiohttp.ClientSession(
            connector=connector,
            timeout=aiohttp.ClientTimeout(total=timeout)
        ) as session:
            url = f"https://{ip}:{port}/cdn-cgi/trace"
            async with session.get(url, headers=headers) as response:
                latency = (time.time() - start_time) * 1000
                
                if response.status == 200:
                    text = await response.text()
                    print(f"  ✓ HTTPS request successful: {latency:.0f}ms")
                    print(f"  Response preview: {text[:100]}...")
                    return latency
                else:
                    print(f"  ✗ HTTPS request failed: HTTP {response.status}")
                    return None
                    
    except Exception as e:
        print(f"  ✗ HTTPS test error: {e}")
        return None

async def main():
    """主函数"""
    # 测试一些已知的Cloudflare IP
    test_ips = [
        "*******",
        "*******", 
        "**************",
        "**************"
    ]
    
    print("=== Cloudflare IP Connection Test ===\n")
    
    successful_ips = []
    
    for ip in test_ips:
        latency = await test_ip_simple(ip)
        if latency:
            successful_ips.append((ip, latency))
        print()
    
    print("=== Results ===")
    if successful_ips:
        print("Successful connections:")
        for ip, latency in sorted(successful_ips, key=lambda x: x[1]):
            print(f"  {ip}: {latency:.0f}ms")
    else:
        print("No successful connections found!")
        print("\nPossible issues:")
        print("1. Network firewall blocking connections")
        print("2. ISP blocking Cloudflare IPs")
        print("3. DNS resolution issues")
        print("4. SSL/TLS certificate issues")

if __name__ == "__main__":
    asyncio.run(main())
