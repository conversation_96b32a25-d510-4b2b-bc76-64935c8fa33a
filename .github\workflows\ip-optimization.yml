name: Cloudflare IP 优化

on:
  # 每6小时自动运行一次
  schedule:
    - cron: '0 */6 * * *'

  # 允许手动触发
  workflow_dispatch:
    inputs:
      ip_sources:
        description: '要使用的IP源（逗号分隔）'
        required: false
        default: 'official,cm,proxyip'
        type: string
      ports:
        description: '要测试的端口（逗号分隔）'
        required: false
        default: '443,2053,2083,2087,2096,8443'
        type: string
      max_ips:
        description: '每个源最大测试IP数量'
        required: false
        default: '512'
        type: string
      concurrent:
        description: '最大并发测试数'
        required: false
        default: '32'
        type: string

jobs:
  optimize-ips:
    runs-on: ubuntu-latest

    steps:
    - name: 检出代码仓库
      uses: actions/checkout@v4
      with:
        token: ${{ secrets.GITHUB_TOKEN }}

    - name: 设置Python环境
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: 安装依赖
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt

    - name: 运行IP优化
      run: |
        # 如果没有通过workflow_dispatch提供参数，则设置默认值
        IP_SOURCES="${{ github.event.inputs.ip_sources || 'official,cm,proxyip' }}"
        PORTS="${{ github.event.inputs.ports || '443,2053,2083,2087,2096,8443' }}"
        MAX_IPS="${{ github.event.inputs.max_ips || '512' }}"
        CONCURRENT="${{ github.event.inputs.concurrent || '32' }}"

        # 将逗号分隔的字符串转换为空格分隔的参数
        IP_SOURCES_ARGS=$(echo "$IP_SOURCES" | tr ',' ' ')
        PORTS_ARGS=$(echo "$PORTS" | tr ',' ' ')

        # 运行优化脚本
        python ip_optimizer.py \
          --sources $IP_SOURCES_ARGS \
          --ports $PORTS_ARGS \
          --max-ips $MAX_IPS \
          --concurrent $CONCURRENT \
          --output results
    
    - name: 生成结果说明文档
      run: |
        cat > results/README.md << 'EOF'
        # Cloudflare IP 优化结果

        此目录包含按国家和性能组织的优化Cloudflare IP地址。

        ## 文件说明

        - `all_results.txt` - 按延迟排序的所有优化IP
        - `summary.json` - JSON格式的统计摘要
        - `{country}_ips.txt` - 按国家代码组织的IP（例如：`us_ips.txt`、`cn_ips.txt`）

        ## 文件格式

        每个IP条目遵循以下格式：
        ```
        IP:端口#国家代码 类型 延迟ms
        ```

        其中：
        - `IP:端口` - IP地址和端口号
        - `国家代码` - 基于Cloudflare托管位置的两字母国家代码
        - `类型` - "official"（Cloudflare官方IP）或"proxy"（反向代理IP）
        - `延迟` - 响应时间（毫秒）

        ## 使用方法

        这些优化的IP可用于提高Cloudflare服务的连接速度。
        选择您所在地区或延迟最低的IP以获得最佳性能。

        ## 最后更新

        由GitHub Actions每6小时自动生成。
        最后运行时间: $(date -u '+%Y-%m-%d %H:%M:%S UTC')

        ## 统计信息

        $(if [ -f results/summary.json ]; then
          echo "### 摘要"
          echo "\`\`\`json"
          cat results/summary.json
          echo "\`\`\`"
        fi)
        EOF
    
    - name: 检查变更
      id: check_changes
      run: |
        if [ -n "$(git status --porcelain results/)" ]; then
          echo "changes=true" >> $GITHUB_OUTPUT
          echo "在结果目录中检测到变更"
        else
          echo "changes=false" >> $GITHUB_OUTPUT
          echo "未检测到变更"
        fi

    - name: 提交并推送结果
      if: steps.check_changes.outputs.changes == 'true'
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"

        # 添加结果目录中的所有文件
        git add results/

        # 创建带时间戳和摘要的提交消息
        TIMESTAMP=$(date -u '+%Y-%m-%d %H:%M:%S UTC')

        # 如果可用，获取摘要信息
        if [ -f results/summary.json ]; then
          TOTAL_IPS=$(python -c "import json; data=json.load(open('results/summary.json')); print(data['total_ips'])")
          COUNTRIES=$(python -c "import json; data=json.load(open('results/summary.json')); print(len(data['countries']))")
          COMMIT_MSG="🚀 IP优化结果 - $TIMESTAMP

          📊 摘要:
          - 优化IP总数: $TOTAL_IPS
          - 覆盖国家数: $COUNTRIES
          - 使用源: ${{ github.event.inputs.ip_sources || 'official,cm,proxyip' }}
          - 测试端口: ${{ github.event.inputs.ports || '443,2053,2083,2087,2096,8443' }}

          由自动化工作流生成"
        else
          COMMIT_MSG="🚀 IP优化结果 - $TIMESTAMP"
        fi

        git commit -m "$COMMIT_MSG"
        git push
    
    - name: 创建发布版本（每周）
      if: steps.check_changes.outputs.changes == 'true' && github.event.schedule == '0 0 * * 0'
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: results-$(date +%Y%m%d)
        release_name: 每周IP优化结果 $(date +%Y-%m-%d)
        body: |
          ## 每周Cloudflare IP优化结果

          此发布版本包含最新的优化Cloudflare IP地址。

          ### 包含内容
          - 按国家组织的优化IP
          - 性能指标和延迟数据
          - 多端口配置测试结果

          ### 使用方法
          下载 `results.zip` 资源并解压以获取特定国家的IP列表。

          自动生成于 $(date -u '+%Y-%m-%d %H:%M:%S UTC')
        draft: false
        prerelease: false

    - name: 上传结果作为构件
      if: always()
      uses: actions/upload-artifact@v3
      with:
        name: optimization-results-${{ github.run_number }}
        path: results/
        retention-days: 30
    
    - name: 清理旧构件
      uses: actions/github-script@v6
      with:
        script: |
          const artifacts = await github.rest.actions.listWorkflowRunArtifacts({
            owner: context.repo.owner,
            repo: context.repo.repo,
            run_id: context.runId,
          });

          // 只保留最新的10个构件
          const sortedArtifacts = artifacts.data.artifacts.sort((a, b) =>
            new Date(b.created_at) - new Date(a.created_at)
          );

          for (let i = 10; i < sortedArtifacts.length; i++) {
            await github.rest.actions.deleteArtifact({
              owner: context.repo.owner,
              repo: context.repo.repo,
              artifact_id: sortedArtifacts[i].id,
            });
          }
