#!/usr/bin/env python3
"""
测试已知可用的Cloudflare IP
"""

import asyncio
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ip_optimizer import CloudflareIPOptimizer

async def test_known_ips():
    """测试已知可用的IP"""
    
    # 从我们的简单测试中知道这些IP是可用的
    known_working_ips = [
        "**************",
        "**************",
        "**************",
        "**************",
        "*************",
        "*************",
        "************",
        "************"
    ]
    
    print("=== Testing Known Working Cloudflare IPs ===\n")
    
    optimizer = CloudflareIPOptimizer(
        max_ips=100,
        timeout=10,
        max_concurrent=5
    )
    
    # 加载Cloudflare位置数据
    await optimizer.load_cloudflare_locations()
    
    # 测试这些已知IP
    results = await optimizer.test_ips_with_concurrency(known_working_ips, 443)
    
    print(f"\n=== Results ===")
    print(f"Total tested: {len(known_working_ips)}")
    print(f"Successful: {len(results)}")
    
    if results:
        print("\nWorking IPs (sorted by latency):")
        for result in results:
            print(f"  {result}")
        
        # 保存结果
        optimizer.save_results(results, "test_results")
        print(f"\nResults saved to test_results/")
    else:
        print("No working IPs found!")

if __name__ == "__main__":
    asyncio.run(test_known_ips())
